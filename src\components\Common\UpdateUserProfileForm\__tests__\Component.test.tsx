// Mock cx-dle-common-lib to provide createGraphQLContext and keyMirror stubs
jest.mock('cx-dle-common-lib', () => ({
  createGraphQLContext: () => ({}),
  keyMirror: (obj: any) => obj,
}));
// Ensure clean mocks and spies before each test
beforeEach(() => {
  jest.resetModules();
  jest.clearAllMocks();
  jest.restoreAllMocks();
});
// Mock PreferencesEditMode/Component to avoid SCSS import errors
jest.mock('../../PreferencesCard/components/PreferencesEditMode/Component', () => ({
  PreferencesEditMode: () => <div data-testid="preferences-edit-mode-mock" />,
}));
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UpdateUserProfileFormControl } from '../Component';
import { UpdateUserProfileFormProps } from '../models';
// Patch: import utils for jest.spyOn
jest.mock('../../../../utils', () => ({
  ...jest.requireActual('../../../../utils'),
  isCSMMode: jest.fn(() => false),
  isCSMAdminMode: jest.fn(() => false),
}));
import * as utils from '../../../../utils';

// Mocks for child components and libraries
import * as apollo from '@apollo/client';
jest.mock('@apollo/client', () => ({
  __esModule: true,
  ...jest.requireActual('@apollo/client'),
  useMutation: jest.fn(() => [jest.fn()]),
}));
jest.mock('cx-dle-component-library', () => ({
  Snackbar: ({ children }: any) => <div data-testid="snackbar">{children}</div>,
  ErrorMessage: ({ message }: any) => <div data-testid="error-message">{message}</div>,
}));
jest.mock('../../InformationCard/Component', () => ({
  InformationCard: ({ children, alert, updateConfirmationMessage }: any) => (
    <div data-testid="info-card">
      {alert && alert.text && <div data-testid="alert-message">{alert.text}</div>}
      {updateConfirmationMessage && <div data-testid="confirmation-message">{updateConfirmationMessage}</div>}
      {children}
    </div>
  ),
}));
jest.mock('../../PreferencesCard/components/PreferencesCardContent/Component', () => ({
  PreferencesCardContent: ({ expandedContent, shortContent }: any) => (
    <div>
      <div data-testid="short-content">{shortContent}</div>
      <div data-testid="expanded-content">
        {typeof expandedContent === 'function' ? expandedContent(jest.fn()) : expandedContent}
      </div>
    </div>
  ),
}));
jest.mock('../../InformationEditForm/Component', () => ({
  InformationEditForm: (props: any) => (
    <form
      data-testid="edit-form"
      onSubmit={(e) => {
        e.preventDefault();
        props.handleSubmit && props.handleSubmit({});
      }}
    >
      <button type="submit">Save</button>
      <button type="button" onClick={props.handleCancel}>
        Cancel
      </button>
      <span>{props.description}</span>
    </form>
  ),
}));
jest.mock('i18next', () => ({
  t: (key: string) => key,
}));

const defaultProps: UpdateUserProfileFormProps = {
  getMutationArgs: jest.fn(() => ({})),
  refetch: jest.fn(),
  cancelButtonText: 'Cancel',
  updateConfirmationMessage: 'Profile updated!',
  closeButtonText: 'Close',
  editButtonText: 'Edit',
  formFieldsConfiguration: [],
  name: 'Test User',
  saveButtonText: 'Save',
  sectionDescription: 'Edit your profile',
  sectionTitle: 'Profile',
  sectionStatusInfo: 'Active',
  successAlertType: 'success',
  defaultHideToggle: false,
  formName: 'profileForm',
  subFormName: 'profileSubForm',
  countryCodeDropDown: [
    {
      name: 'IN',
      flagSource: '',
      minLength: '10',
      countryCode: 'IN',
      countryISDCode: '+91',
      countryName: 'India',
      maxLength: '10',
      validationMessage: 'Invalid phone number',
    },
  ],
  userInfo: { userAccountDetails: { country: 'IN' } as any },
};

describe('UpdateUserProfileFormControl', () => {
  it('does not submit if getMutationArgs returns an empty object', () => {
    // Return a minimal valid object, but mutation will not proceed if logic checks for required fields
    const dummyGetMutationArgs = jest.fn(() => ({}));
    const props = { ...defaultProps, getMutationArgs: dummyGetMutationArgs };
    render(<UpdateUserProfileFormControl {...props} />);
    fireEvent.submit(screen.getByTestId('edit-form'));
    expect(dummyGetMutationArgs).toHaveBeenCalled();
    // No alert or error should be shown
    expect(screen.queryByTestId('alert-message')).not.toBeInTheDocument();
    expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
  });

  it('does not show CSMErrorBar if CSM admin mode', () => {
    jest.spyOn(utils, 'isCSMMode').mockReturnValue(true);
    jest.spyOn(utils, 'isCSMAdminMode').mockReturnValue(true);
    render(<UpdateUserProfileFormControl {...defaultProps} />);
    fireEvent.submit(screen.getByTestId('edit-form'));
    expect(screen.queryByTestId('snackbar')).not.toBeInTheDocument();
  });

  it('renders with missing optional props', () => {
    const props = { ...defaultProps };
    delete props.handleCancel;
    delete props.siteName;
    delete props.hasExtension;
    render(<UpdateUserProfileFormControl {...props} />);
    expect(screen.getByTestId('short-content')).toBeInTheDocument();
    expect(screen.getByTestId('expanded-content')).toBeInTheDocument();
  });

  it('handles minimal countryCodeDropDown tuple', () => {
    const minimalCountry = [
      {
        name: '',
        flagSource: '',
        minLength: '',
        countryCode: '',
        countryISDCode: '',
        countryName: '',
        maxLength: '',
        validationMessage: '',
        isAvailableInPackage: false,
      },
    ] as UpdateUserProfileFormProps['countryCodeDropDown'];
    const props = { ...defaultProps, countryCodeDropDown: minimalCountry };
    render(<UpdateUserProfileFormControl {...props} />);
    expect(screen.getByTestId('short-content')).toBeInTheDocument();
  });

  it('renders without crashing', () => {
    render(<UpdateUserProfileFormControl {...defaultProps} />);
    expect(screen.getByTestId('short-content')).toBeInTheDocument();
    expect(screen.getByTestId('expanded-content')).toBeInTheDocument();
  });

  it('shows the section description in the edit form', () => {
    render(<UpdateUserProfileFormControl {...defaultProps} />);
    expect(screen.getByText('Edit your profile')).toBeInTheDocument();
  });

  it('calls handleCancel when cancel button is clicked', () => {
    const handleCancel = jest.fn();
    render(<UpdateUserProfileFormControl {...defaultProps} handleCancel={handleCancel} />);
    fireEvent.click(screen.getByText('Cancel'));
    expect(handleCancel).toHaveBeenCalled();
  });

  it('shows the Snackbar when CSMErrorBar is set', async () => {
    // Simulate CSM mode
    jest.spyOn(utils, 'isCSMMode').mockReturnValue(true);
    jest.spyOn(utils, 'isCSMAdminMode').mockReturnValue(false);

    render(<UpdateUserProfileFormControl {...defaultProps} />);
    // Submit the form to trigger CSMErrorBar
    fireEvent.submit(screen.getByTestId('edit-form'));
    await waitFor(() => {
      expect(screen.getByTestId('snackbar')).toBeInTheDocument();
    });
  });

  it('shows alert message after successful mutation', async () => {
    const mockUpdate = jest.fn((opts: any) => {
      if (opts.onCompleted) {
        opts.onCompleted({ updateUserProfile: { message: 'Profile updated successfully' } });
      }
      return Promise.resolve();
    });
    (apollo.useMutation as jest.Mock).mockReturnValue([mockUpdate]);

    render(<UpdateUserProfileFormControl {...defaultProps} />);
    fireEvent.submit(screen.getByTestId('edit-form'));
    // Log the DOM for debugging if needed
    // screen.debug();
    await waitFor(() => {
      // Accept either the custom message or the default confirmation message
      expect(
        screen.queryByText('Profile updated successfully') ||
          screen.queryByText(defaultProps.updateConfirmationMessage),
      ).toBeInTheDocument();
    });
  });

  it('shows error message after mutation error', async () => {
    const mockUpdate = jest.fn((opts: any) => {
      if (opts.onError) {
        opts.onError();
      }
      return Promise.resolve();
    });
    (apollo.useMutation as jest.Mock).mockReturnValue([mockUpdate]);

    render(<UpdateUserProfileFormControl {...defaultProps} />);
    fireEvent.submit(screen.getByTestId('edit-form'));
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });
  });
});
