import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.scss';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { ApolloProvider } from '@apollo/client';
import {
  AnalyticsEngine,
  createApolloClient,
  AnalyticsProvider,
  linkClick,
  withAnalytics,
  compose,
} from 'cx-dle-common-lib';
import config from './config';
import { getLocaleInfoFromHostName, isAuthenticated, isCSMMode, isEUSite, isLASite } from './utils';
import { initLocaleMoment } from './utils/momentWrapper';
import SessionRefreshComponent from './components/SessionRefresh';
import { CSM_USER_SEARCH_URL } from './constants';
import { isBlockedAnalyticsUrl } from './utils/commonUtils';
import getUserDetailsFromCookies from './utils/helpers';

const AccountApp = compose(withAnalytics([linkClick]))(App);

const analyticsEngine = {
  engine: new AnalyticsEngine(),
};

const apolloClient = createApolloClient({
  uri: isLASite()
    ? config.latamGraphQLUri
    : isEUSite()
      ? config.emeaGraphQLUri
      : config.defaultGraphQLUri,
  timeout: config.defaultGraphQLTimeout,
  ssr: false,
});

const localeInfo = getLocaleInfoFromHostName();
const impersonationDetails = localStorage.getItem('impersonationDetails');

const render = async () => {
  await initLocaleMoment(localeInfo?.momentLocale);

  // Check for blocked analytics URLs before any rendering
  try {
    const currentPath = window.location.pathname;
    const user = getUserDetailsFromCookies();
    const userCountry = isCSMMode()
      ? user?.userAccountDetails?.country
      : user?.address?.country;

    console.log('Checking blocked analytics URL:', {
      currentPath,
      userCountry,
      isCSMMode: isCSMMode(),
      user: user ? 'User found' : 'No user'
    });

    if (isBlockedAnalyticsUrl(currentPath, userCountry)) {
      // Redirect to main account page with proper leading slash
      const redirectPath = config?.accountPageRoute?.startsWith('/')
        ? config.accountPageRoute
        : `/${config?.accountPageRoute || 'account'}`;

      console.log('🚫 Blocked analytics URL detected - redirecting to:', redirectPath);
      window.location.replace(redirectPath);
      return; // Prevent further execution
    } else {
      console.log('✅ URL is allowed - proceeding normally');
    }
  } catch (error) {
    console.error('Error checking blocked analytics URL:', error);
  }

  const authenticated = isAuthenticated();
  const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

  if (isCSMMode() === true && !impersonationDetails) {
    const languageCode = [
      'en-ca',
      'fr-ca',
      'en-africa',
      'fr-africa',
      'fr-fr',
      'en-ph',
      'en-my',
      'en-th',
      'en-sg',
      'middle-east',
    ];
    const virtualPath = localeInfo.localeCode;
    const parentHostName = window.location.origin;
    const searchUserUrl = `${virtualPath}${CSM_USER_SEARCH_URL}`;
    window.location.href = languageCode.includes(virtualPath)
      ? `${parentHostName}/${searchUserUrl}`
      : `${parentHostName}${CSM_USER_SEARCH_URL}`;
    return;
  }

  if (authenticated) {
    root.render(
      <React.StrictMode>
        <AnalyticsProvider value={analyticsEngine}>
          <ApolloProvider client={apolloClient}>
            <AccountApp />
          </ApolloProvider>
        </AnalyticsProvider>
      </React.StrictMode>,
    );
  } else {
    root.render(<SessionRefreshComponent refreshCallback={render} />);
  }
};

render();

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
