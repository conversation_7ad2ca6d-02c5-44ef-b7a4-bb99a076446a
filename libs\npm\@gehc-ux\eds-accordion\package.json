{"name": "@gehc-ux/eds-accordion", "license": "SEE LICENSE IN @GEHC-UX/eds-accordion/licensing", "description": "Accordion Component for Elements Design System for GE Healthcare", "version": "1.0.0", "keywords": ["accordion"], "componentType": "sass", "scripts": {"prebuild": "npm run get-angular-build", "postbuild": "node node_modules/@gehc-ux/eds-tools/component-build/clean-dist.js --name=\"accordion\"", "build": "ng build --prod", "get-angular-build": "node node_modules/@gehc-ux/eds-tools/component-build/copy.js --name=\"accordion\"", "pretest": "npm run test:generate-colormap && npm run test:generate-theme-vars && npm run get-angular-build", "pretest-watch": "npm run test:generate-colormap && npm run test:generate-theme-vars && npm run get-angular-build", "test:generate-colormap": "node node_modules/@gehc-ux/eds-tools/tests/generate-color-map.js", "test:generate-colormap:force": "node node_modules/@gehc-ux/eds-tools/dist/generate-color-map.js --force-regen", "test:generate-theme-vars": "cd node_modules/@gehc-ux/eds-tools && node tests/kickoff-webpack.js && node dist/generate-theme-variables-map", "test": "ng test --code-coverage", "test-watch": "ng test --code-coverage --watch --browsers Chrome", "test-report": "http-server -c-1 -o -p 9875 ./coverage"}, "repository": {"type": "git", "url": "https://github.build.ge.com/gehc-ux/eds-accordion"}, "devDependencies": {"@gehc-ux/eds-typography": "^1.0.0", "@gehc-ux/eds-tools": "^1.0.0", "@gehc-ux/eds-theme-switch": "^1.4.0"}, "dependencies": {"@gehc-ux/eds-icon": "^1.0.0", "@gehc-ux/eds-flexbox": "^1.0.0", "@gehc-ux/eds-button": "^1.0.0", "@gehc-ux/eds-spacing": "^1.0.0", "@gehc-ux/eds-spacing-responsive": "^1.0.0", "@gehc-ux/eds-widths": "^1.0.0", "@gehc-ux/eds-widths-responsive": "^1.0.0", "@gehc-ux/eds-core": "^1.3.0"}}