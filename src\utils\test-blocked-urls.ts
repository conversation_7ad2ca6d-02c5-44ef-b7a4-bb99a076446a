// Test file to verify blocked analytics URL functionality
import { isBlockedAnalyticsUrl } from './commonUtils';

// Test cases for blocked analytics URLs
export const testBlockedAnalyticsUrls = () => {
  console.log('Testing blocked analytics URL functionality...');
  
  const testCases = [
    // Should be blocked for US/CA users
    { url: '/account/serviceperformance', country: 'US', expected: true },
    { url: '/serviceperformance', country: 'CA', expected: true },
    { url: '/service-performance', country: 'CA-FR', expected: true },
    { url: '/analyze-service-performance', country: 'US', expected: true },
    { url: '/en-us/account/serviceperformance', country: 'US', expected: true },
    { url: '/fr-ca/serviceperformance', country: 'CA', expected: true },
    
    // Should NOT be blocked for other countries
    { url: '/account/serviceperformance', country: 'FR', expected: false },
    { url: '/serviceperformance', country: 'DE', expected: false },
    { url: '/service-performance', country: 'JP', expected: false },
    
    // Should NOT be blocked for other URLs
    { url: '/account/settings', country: 'US', expected: false },
    { url: '/account/notifications', country: 'CA', expected: false },
    { url: '/account', country: 'US', expected: false },
    
    // Edge cases
    { url: '', country: 'US', expected: false },
    { url: '/account/serviceperformance', country: '', expected: false },
    { url: '/account/serviceperformance', country: null, expected: false },
  ];
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = isBlockedAnalyticsUrl(testCase.url, testCase.country as string);
    const success = result === testCase.expected;
    
    if (success) {
      passed++;
      console.log(`✅ Test ${index + 1}: PASSED`);
    } else {
      failed++;
      console.log(`❌ Test ${index + 1}: FAILED`);
      console.log(`   URL: ${testCase.url}`);
      console.log(`   Country: ${testCase.country}`);
      console.log(`   Expected: ${testCase.expected}`);
      console.log(`   Got: ${result}`);
    }
  });
  
  console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
  return { passed, failed };
};

// Function to simulate URL navigation and test redirection
export const simulateBlockedUrlNavigation = (url: string, country: string) => {
  console.log(`Simulating navigation to: ${url} for user in country: ${country}`);
  
  if (isBlockedAnalyticsUrl(url, country)) {
    console.log('🚫 URL is blocked - would redirect to /account');
    return '/account';
  } else {
    console.log('✅ URL is allowed - would proceed normally');
    return url;
  }
};
