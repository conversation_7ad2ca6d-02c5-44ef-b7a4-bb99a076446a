const path = require('path');
const paths = require('./paths/account.paths.js');

module.exports = function () {
  return {
    compress: true,
    static: {
      directory: paths.appPublic,
      publicPath: paths.publicPath,
      watch: true,
    },
    historyApiFallback: {
      disableDotRule: true,
      index: paths.publicPath,
    },
    devMiddleware: {
      publicPath: paths.publicPath,
    },
    hot: true,
    open: true,
    host: 'localhost',
    port: 3000,
    server: 'https',
    client: {
      overlay: {
        errors: true,
        warnings: false,
      },
      logging: 'info',
    },
    setupMiddlewares: (middlewares, devServer) => {
      if (!devServer) {
        throw new Error('webpack-dev-server is not defined');
      }

      devServer.app.get('/', function (req, res) {
        res.sendFile(path.join(paths.appPublic, 'index.html'));
      });

      return middlewares;
    },
  };
};
