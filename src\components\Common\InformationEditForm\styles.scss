@import '~cx-dle-component-library/styles/core/functions';
@import '~cx-dle-component-library/styles/core/variables';
@import '~cx-dle-component-library/styles/core/mixins';

.information-edit-form {
  @include tablet {
    padding: 0 $spacer * 2;
  }
    
  &__row {
    padding-bottom: $spacer * 3;
  }

  &__field {
    max-width: 320px;
  }

  &__actions {
    display: flex;
    padding-bottom: $spacer * 2;
  }

  &__button {
    min-width: 128px;
    line-height: 1.2;
    @include phone {
      min-width: 85px;
      font-size: $spacer * 1.375;
      padding: 0;
    }
  }

  &__submit {
    margin-right: $spacer * 3;
  }

  &__description {
    color: $typography-secondary;
    @include phone {
      font-size: $font-size-sm;
    }
    @include tablet-only {
      max-width: 50%;
    }
    @include desktop {
      max-width: 70%;
    }
  }

}
.information-edit-form__actions{
  display: flex;
}

[class*="extension_added"] {
   display: grid;
   grid-template-columns: auto auto;
   gap: 10px;
   align-items: flex-start;
  }
