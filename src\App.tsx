import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Layout, Redirect, ProtectedRoute } from './components';
import { LanguageProvider } from './context/LanguageContext';
import config from './config';
import { initI18n } from './i18n/config';
import Settings from './Pages/Settings';

import AvuriDeviceRegistration from './Pages/AvuriDeviceRegistration';
import Account from './Pages/Account';
import NotificationsList from './Pages/Notifications';
import { LDProvider } from 'launchdarkly-react-client-sdk';
import getUserDetailsFromCookies, { isCSMMode } from './utils/helpers';
import MyTeams from './Pages/MyTeams';
import SetupAccount from './Pages/AccountSetup';
import { SubscriptionClientProvider } from './context/SubscriptionClientContext';
// import LoginProblem from './Pages/loginProblem';
import { isBlockedAnalyticsUrl } from './utils/commonUtils';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      staleTime: Infinity,
    },
  },
});

const App: React.FC = () => {
  const [isI18nInitialized, setIsI18nInitialized] = useState(false);

  useEffect(() => {
    const init = async () => {
      try {
        await initI18n();
        setIsI18nInitialized(true);
      } catch (error) {
        console.error('Failed to initialize i18n:', error);
        setIsI18nInitialized(true);
      }
    };
    init();
  }, []);

  // Separate useEffect for URL blocking check to ensure it runs before routing
  useEffect(() => {
    try {
      const currentPath = window.location.pathname;
      const user = getUserDetailsFromCookies();
      const userCountry = isCSMMode()
        ? user?.userAccountDetails?.country
        : user?.address?.country;

      console.log('App.tsx: Checking blocked analytics URL:', {
        currentPath,
        userCountry,
        isCSMMode: isCSMMode(),
        user: user ? 'User found' : 'No user'
      });

      if (isBlockedAnalyticsUrl(currentPath, userCountry)) {
        // Redirect to main account page with proper leading slash
        const redirectPath = config?.accountPageRoute?.startsWith('/')
          ? config.accountPageRoute
          : `/${config?.accountPageRoute || 'account'}`;

        console.log('App.tsx: Blocked analytics URL detected - redirecting to:', redirectPath);
        window.location.replace(redirectPath);
        return; // Prevent further execution
      } else {
        console.log('App.tsx: URL is allowed - proceeding normally');
      }
    }
    catch (error) {
      console.error('App.tsx: Error checking blocked analytics URL:', error);
    }
  }, []);

  const userInfo = getUserDetailsFromCookies();
  const LDContext = {
    "kind": "user",
    "key": userInfo?.email,
    "name": userInfo?.email,
    "email": userInfo?.email
  };
  

  if (!isI18nInitialized) {
    return null; // Or a loading spinner
  }



  return (
    <LDProvider context={LDContext} clientSideID={config.launchDarklyClientSideId} reactOptions={{useCamelCaseFlagKeys:false}}>
      <QueryClientProvider client={queryClient}>
        <ReactQueryDevtools initialIsOpen={false} position="left" />
        <Router>
          <Routes>
            {/* This path will match URLs like /v1, /en-US/v1, /pt-BR/v1 */}
            <Route
              path="/:lang?/"
              element={
                <ProtectedRoute>
                  <LanguageProvider>
                    <SubscriptionClientProvider>
                    <Layout />
                   </SubscriptionClientProvider>
                  </LanguageProvider>
                </ProtectedRoute>
              }
            >
              {/* Dynamic language route */}
              <Route path={config?.accountPageRoute} element={<Account />} />
              <Route path={config?.notificationPageRoute} element={<NotificationsList />} />
              <Route path={config?.settingsPageRoute} element={<Settings />}/>
              <Route path={config?.accountSetupPageRoute} element={<SetupAccount />} />
              <Route path={config?.avuriRegistrationPageRoute} element={<AvuriDeviceRegistration />} />
              <Route path={config?.accountMyTeamPageRoute} element={<MyTeams />} />    
              {/* <Route path={config?.loginProblemRoute} element={<LoginProblem />} />            */}
              
              {/* Catch-all redirect 404 for unknown routes */}
              <Route path="*" element={<Redirect to={config?.errorPages?.error404} />} />
            </Route>
          </Routes>
        </Router>
      </QueryClientProvider>
    </LDProvider>
  );
};

export default App;
