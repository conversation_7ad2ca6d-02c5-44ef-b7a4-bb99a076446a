import { FORM_FIELDS } from './constants';
import { PhoneSectionProps } from './models';
import { InformationFormField } from '../LanguageSection/models';
import { FormValues } from 'cx-dle-common-lib';
import UpdateUserProfileFormControl from '../../../Common/UpdateUserProfileForm';
import { useEffect, useState } from 'react';
import { PHONE } from '../../../../constants';

export const PhoneSection = ({
  phone,
  phoneLabelText,
  phoneSectionDescription,
  emptyNewPhoneErrorMessage,
  newPhoneLabelText,
  newPhoneHelperText,
  newPhonePlaceholderText,
  updateConfirmationMessage,
  requiredLabelText,
  formName,
  subFormName,
  phoneExtension,
  countryCode,
  ISDCode,
  ...other
}: PhoneSectionProps) => {
  const [formField, setFormField] = useState<InformationFormField[]>([]);
  const formFieldsConfiguration: InformationFormField[] = [
    {
      hintText: newPhoneHelperText,
      name: FORM_FIELDS.PHONE,
      placeholder: newPhonePlaceholderText,
      requiredLabelText,
      requiredMessageText: emptyNewPhoneErrorMessage,
      title: newPhoneLabelText,
      type: 'text',
      value: phone,
    },
  ];

  const PhoneExtensionFieldConfiguration: InformationFormField[] = [
    {
      hintText: '',
      name: FORM_FIELDS.EXTENSION,
      placeholder: '',
      requiredLabelText: other.extension.phoneExtensionOptionLabel,
      requiredMessageText: '',
      title: other.extension.phoneExtensionLabelText,
      type: 'text',
      value: phoneExtension || '',
      validationExtension:
        other?.extension?.validations.find((item: any) => item.supportedCountries.includes(countryCode)) ||
        other?.extension?.validations[0],
    },
  ];
  const getUpdatedFormFieldsConfiguration = () => {
    const isCountryCode = other?.extension?.validations.some((item) => item.supportedCountries.includes(countryCode));
    return isCountryCode ? [...formFieldsConfiguration, ...PhoneExtensionFieldConfiguration] : formFieldsConfiguration;
  };

  useEffect(() => {
    setFormField(getUpdatedFormFieldsConfiguration());
  }, [phone, phoneExtension]);

  const getMutationArgs = (values: FormValues): any => ({
    phone: values[FORM_FIELDS.PHONE],
    extension: values[FORM_FIELDS.EXTENSION],
  });

  const doesFieldExist = (fieldName: string) => {
    return formField.some((field) => field.name === fieldName);
  };

  return (
    <>
      <UpdateUserProfileFormControl
        formName={formName}
        subFormName={subFormName}
        sectionTitle={phoneLabelText}
        sectionDescription={phoneSectionDescription}
        formFieldsConfiguration={formField as any}
        updateConfirmationMessage={updateConfirmationMessage}
        getMutationArgs={getMutationArgs}
        hasExtension={ISDCode}
        {...other}
        refetch={async () => {
          await other.refetch();
        }}
        id={doesFieldExist(FORM_FIELDS.EXTENSION) ? PHONE : ''}
        extension={phoneExtension}
        sectionExtensionTitle={other?.extension?.phoneExtensionLabelText}
        ISDCode={ISDCode}
      >
        {phone}
      </UpdateUserProfileFormControl>
    </>
  );
};
