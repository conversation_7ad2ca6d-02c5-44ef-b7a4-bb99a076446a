import React from 'react';
import { render, screen } from '@testing-library/react';
import { InformationEditForm } from '../Component';
import { InformationEditFormProps } from '../models';

jest.mock('cx-dle-common-lib', () => ({
  keyMirror: (keys: any) =>
    Object.keys(keys).reduce((acc: any, key: string) => {
      acc[key] = key;
      return acc;
    }, {} as any),
  Utils: {
    getCookie: jest.fn(() => 'mock-id-token'),
    getJwtPayload: jest.fn(() => ({ address: { country: 'IN' } })),
  },
  AUTHENTICATION: {
    idTokenCookie: 'idToken',
  },
  validationSchema: jest.fn(() => ({
    required: jest.fn(() => ({
      email: jest.fn(() => true),
    })),
  })),
  OMNITURE_EVENTS: {
    LINK_CLICK: 'link_click',
  },
  prepareDataAnalyticsAttributes: jest.fn(() => ({})),
  Form: ({ children, submitHandler }: any) => (
    <form
      onSubmit={(e: any) => {
        e.preventDefault();
        submitHandler({ values: {} }, { toggleSubmitting: jest.fn() });
      }}
    >
      {children}
    </form>
  ),
  FormElement: ({ name, children }: any) =>
    children({
      handleChange: jest.fn(),
      handleBlur: jest.fn(),
      handleFocus: jest.fn(),
      touched: { [name]: false },
      valid: { [name]: true },
    }),
}));

jest.mock('../../../../constants', () => ({
  FORM_FIELDS: {
    FIRST_NAME: 'FIRST_NAME',
    LAST_NAME: 'LAST_NAME',
  },
  CONTROL_KEYS: [],
}));

jest.mock('@tanstack/react-query', () => ({
  ...jest.requireActual('@tanstack/react-query'),
  useQuery: jest.fn(() => ({
    data: {
      countryCode: 'IN',
      countryName: 'India',
      websiteCountryCode: 'IN',
    },
    isLoading: false,
    isError: false,
  })),
}));

jest.mock('../../../../context/LanguageContext', () => ({
  useLanguage: () => ({
    language: 'en-US',
    setLanguage: jest.fn(),
  }),
}));

jest.mock('../../GeTextInput/Component', () => ({
  __esModule: true,
  GeTextInput: (props: any) => (
    <input aria-label={props.labelText} name={props.name} defaultValue={props.defaultValue} />
  ),
  default: (props: any) => <input aria-label={props.labelText} name={props.name} defaultValue={props.defaultValue} />,
}));

jest.mock('../../GeSelect/Component', () => ({
  __esModule: true,
  GeSelect: (props: any) => <select aria-label={props.labelText} name={props.name} defaultValue={props.defaultValue} />,
  default: (props: any) => <select aria-label={props.labelText} name={props.name} defaultValue={props.defaultValue} />,
}));

jest.mock('cx-dle-component-library', () => ({
  GeButton: (props: any) => (
    <button type={props.type || 'button'} onClick={props.onClick} disabled={props.disabled}>
      {props.btnLabel}
    </button>
  ),
  FormMessage: () => null,
  PhoneExtensionInput: () => null,
  PhoneNumberWithCountryCodeV1: () => null,
  RichText: (props: any) => <div>{props.text}</div>,
}));

jest.mock('../FormConnector', () => ({
  FormConnector: ({ children }: any) => {
    const mockContext = {
      ResetForm: jest.fn(),
      SetTouched: jest.fn(),
      form: {
        fields: {},
        changed: {},
        values: {},
        valid: {},
        errors: {},
        touched: {},
        status: {
          dirty: false,
          valid: false,
          submitting: false,
          lastFieldChanged: '',
        },
      },
    };
    return children(mockContext);
  },
}));

const mockHandleSubmit = jest.fn(() => Promise.resolve());
const mockHandleCancel = jest.fn();

const defaultProps: InformationEditFormProps = {
  analyticsAttributes: [],
  description: 'Edit your information',
  saveButtonText: 'Save',
  cancelButtonText: 'Cancel',
  fields: [
    {
      title: 'First Name',
      name: 'firstName',
      type: 'text',
      value: '',
      requiredLabelText: '*',
      requiredMessageText: 'First name is required',
    },
  ],
  isSubmitting: false,
  handleSubmit: mockHandleSubmit,
  handleCancel: mockHandleCancel,
  countryCodeDropDown: [
    {
      flagSource: '',
      minLength: '10',
      maxLength: '10',
      countryCode: 'IN',
      countryISDCode: '+91',
      countryName: 'India',
      validationMessage: 'Invalid phone number',
    },
  ],
};

describe('InformationEditForm Component', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders description and field label', () => {
    render(<InformationEditForm {...defaultProps} />);
    expect(screen.getByText('Edit your information')).toBeInTheDocument();
    expect(screen.getByLabelText(/First Name/i)).toBeInTheDocument();
  });
});
