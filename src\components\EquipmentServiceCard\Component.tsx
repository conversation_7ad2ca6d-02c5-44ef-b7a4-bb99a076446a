import { OMNITURE_EVENTS, prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import { EDSIcon, GeButton } from 'cx-dle-component-library';
import { Icon } from 'cx-dle-component-library/components/EDS/models';
import parse from 'html-react-parser';
import './styles.scss';
import { PROGRESSIVE_REGISTRATION_STATUS } from '../../constants';
import { EquipmentServiceCardProps, PopupDataType } from './models';
import EquipmentStatusMessage from '../EquipmentStatusMessage';
import SetupYourFleetPopup from '../SetupYourFleetPopup';
import { HTMLAttributeAnchorTarget, useMemo } from 'react';
import { EnableServicesIfOrdersNotApproveType } from '../../models/Account';
import { getUserSpecificLink, isCSMReadOnly } from '../../utils';
import { shouldShowComponent } from '../../utils/commonUtils';

const VERISOUND_FLEET_CARD_NAME = 'Log in to Verisound Fleet';
const dummyArray = [0, 1, 2, 3, 4];
const setupYourFleetClass = 'ico-hospital-32';

export const EquipmentServiceCard = ({
  cardData: data,
  title,
  hasAccess,
  pendingLabel,
  rejectedLabel,
  popupData,
  isSetupPopUpVisible,
  status,
  loading,
  setupBtn,
  setupAmount,
  totalAmount,
  equipmentServiceLabel,
  otherServiceLabel,
  hasVerisoundFleetAccess,
  defaultCountry,
  checkCSMMode,
  userMailingCountry,
  isHoldingAccount,
}: EquipmentServiceCardProps) => {
  console.log(userMailingCountry,"jjj");
  console.log(data,"jjjyyy");
  const cardData = useMemo(() => { 
    return data.filter((d) => {
        if(!userMailingCountry) {
          return true;
        }
        
        // Remove "Analyze Service Performance" card for USCAN customers
        const isAnalyzeServicePerformance = d.name === 'Analyze Service Performance' || 
                                           d.title === 'Analyze Service Performance' || d.name=== 'Analyser la performance de maintenance' ;
        const isUSCANRegion = ['US', 'CA', 'CA-FR'].includes(userMailingCountry);
        
        if (isAnalyzeServicePerformance && isUSCANRegion) {
          return false;
        }
        
        return true;
      })
.map((d) => ({
      ...d,
      link: getUserSpecificLink(d.link, defaultCountry, userMailingCountry),
      alternateLink: getUserSpecificLink(d.alternateLink, defaultCountry),
    }));
  }, [data,userMailingCountry]);

  const replaceTrademark = (value: string) => {
    return parse(value.replaceAll('{TM}', '&trade;'));
  };

  const isApplicationPending = (status: string) => {
    return status === PROGRESSIVE_REGISTRATION_STATUS.IN_PROGRESS || status === PROGRESSIVE_REGISTRATION_STATUS.PENDING;
  };

  const getLinkTitleForAnalytic = () => {
    let title = '';
    if (equipmentServiceLabel) {
      title = equipmentServiceLabel;
    } else if (otherServiceLabel) {
      title = otherServiceLabel;
    }
    return title;
  };

  const addNameToDescription = (text: any = '', id = '') => {
    if (setupYourFleetClass === id) {
      const setupAmountBold = `<b>${setupAmount?.toString()}</b>`;
      const updatedText = text.replace('{0}', setupAmountBold).replace('{1}', totalAmount?.toString());
      return parse(updatedText);
    } else {
      return text;
    }
  };

  const renderCardContent = (item: EnableServicesIfOrdersNotApproveType) => {
    return (
      <>
        <div
          className={
            hasAccess && item.link.class?.toLowerCase() === 'ico-tools-32'
              ? 'equipment-service-card__img-wrap-request-service__icon'
              : hasAccess
              ? 'equipment-service-card__img-wrap'
              : 'equipment-service-card__img-wrap-inline'
          }
        >
          <EDSIcon icon={item.link.class as Icon} />
        </div>

        <div>
          <div className="equipment-service-card__title">{replaceTrademark(item.title)}</div>
          {hasAccess && (
            <div className="equipment-service-card__description">
              {addNameToDescription(replaceTrademark(item?.description), item.link?.class)}
            </div>
          )}
        </div>
      </>
    );
  };

  if (loading) {
    return (
      <div className="equipment-service-card__boxed" data-testid="equipment-service-card-loading">
        {<h6 className="main-title equipment-service-card__loading"></h6>}
        <ul className="equipment-service-card__list equipment-service-card__list-inline">
          {dummyArray.map((i) => (
            <li className="equipment-service-card__loading equipment-service-card__loading-list" key={i}></li>
          ))}
        </ul>
      </div>
    );
  } else
    return (
      <div className={!hasAccess ? 'equipment-service-card__boxed' : ''}>
        {!!title && !hasAccess && <h6 className="main-title">{title}</h6>}

        {!hasAccess && (
          <EquipmentStatusMessage pendingLabel={pendingLabel} rejectedLabel={rejectedLabel} status={status} />
        )}

        <ul className={`equipment-service-card__list ${hasAccess ? '' : 'equipment-service-card__list-inline'}`}>
          {cardData.length > 0 &&
            cardData.map((item) => {
              let hrefVal = item.link.href;
              let targetVal = item.link.target || null;
              item.name == VERISOUND_FLEET_CARD_NAME &&
                hasVerisoundFleetAccess && !isHoldingAccount &&
                ((hrefVal = item.alternateLink.href),
                (targetVal = item.alternateLink.target ? item.alternateLink.target : null));
              if (shouldShowComponent(item?.supportedCountries, userMailingCountry)) {
                return (
                  <li
                    key={item.name}
                    className={hasAccess ? 'equipment-service-card__item' : 'equipment-service-card__item-inline'}
                  >
                    <a
                      className={`equipment-service-card__cta-link ${
                        hasAccess ? '' : 'equipment-service-card__cta-link--inline'
                      }`}
                      href={hasAccess ? hrefVal : ''}
                      target={hasAccess ? (targetVal as HTMLAttributeAnchorTarget) : undefined}
                      {...prepareDataAnalyticsAttributes([
                        { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
                        { name: 'linkName', value: item.title },
                        { name: 'linkType', value: 'Equipment Service Card Component' },
                        { name: 'linkTitle', value: getLinkTitleForAnalytic() },
                      ])}
                    >
                      {renderCardContent(item)}
                    </a>
                    {hasAccess && setupYourFleetClass === item.link?.class && isSetupPopUpVisible && (
                      <div className="equipment-service-card__popup-wrapper">
                        <SetupYourFleetPopup popupData={popupData as PopupDataType} isSetupPopUpVisible />
                      </div>
                    )}
                  </li>
                );
              }
            })}
        </ul>
        {!!setupBtn && !isApplicationPending(status || '') && !hasAccess && (
          <GeButton
            className="equipment-service-card__setup-btn"
            btnLabel={setupBtn.text}
            onClick={(e) => {
              if (isCSMReadOnly()) {
                checkCSMMode?.(e);
              } else {
                window.location.href = setupBtn.href.toString();
              }
            }}
            {...prepareDataAnalyticsAttributes([
              { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
              { name: 'linkName', value: setupBtn.text },
              { name: 'linkType', value: 'equipment inventory and services' },
              { name: 'linkTitle', value: 'equipment setup' },
            ])}
          />
        )}
      </div>
    );
};
