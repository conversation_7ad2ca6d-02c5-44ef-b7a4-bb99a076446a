import { FormConnector, FormElement, validationSchema } from 'cx-dle-common-lib';
import { EDSIcon, FormMessage, FormSection, GridCell, GridRow } from 'cx-dle-component-library';
import { PhoneNumberWithCountryCodeV1 } from 'cx-dle-component-library';

const spaceRegex = /\s/g;
const numericRegex = /\D/g;

import parseHtml from 'html-react-parser';
import { FORM_FIELDS, GEIDPSERVICESHOP, GLOBAL_SIGNUP_PAGE } from '../../constants';

import { IntroduceyourselfProps } from './models';

import { useState } from 'react';
import { renderFormFieldsByLocale } from '../../utils/commonUtils';
import GeTextInput from '../Common/GeTextInput';
import { INTRO_FIELDS_MAP } from './constants';
import './styles.scss';

const getInputLabel = (field: string, required: boolean) => {
  if (required) {
    return parseHtml(`${field} <span class='ge-material-form-input-label__required'> *</span>`);
  }
  return field;
};

export const IntroduceYourself = ({
  defaultValues,
  websiteCountryCode,
  globalCountry,
  ...props
}: IntroduceyourselfProps) => {
  const sourceApplications = props.sourceApplication ? props.sourceApplication?.toLowerCase() : '';
  const [isPhoneNumberError, setIsPhoneNumberError] = useState(false);

  const isSSSource = sourceApplications === GEIDPSERVICESHOP;

  const country = window.location.pathname.includes(GLOBAL_SIGNUP_PAGE) //added this condition to have preferred country as default country for global signup instead of website country code
    ? props.preferredCountryCode
    : websiteCountryCode;
  const defaultCountryCode = globalCountry || country;

  return (
    <FormSection className="introduce-yourself" titleText={props.introduceYourselfCaption}>
      <GridRow>
        <FormConnector>
          {({ SetValue }) => {
            return (
              <>
                <GridCell desktop={12} tablet={12}>
                  <GridRow>
                    {renderFormFieldsByLocale(
                      [
                        <GridCell tablet={12} desktop={6} key={FORM_FIELDS.FIRST_NAME}>
                          <GeTextInput
                            className="text-required"
                            labelText={props.firstNameLabel}
                            hintText={props.firstNameHelperTextLabel}
                            requiredText={props.firstNameOptionalRequiredLabel}
                            name={FORM_FIELDS.FIRST_NAME}
                            defaultValue={defaultValues[FORM_FIELDS.FIRST_NAME]}
                            placeHolder={props.firstNamePlaceholder}
                            maxLength={Number(props.firstNameMaxCharacters)}
                            required={!props.firstNameOptional}
                            validate={
                              !props.firstNameOptional
                                ? validationSchema()
                                    .required(props.firstNameRequiredValidationMessage || 'First Name is required')
                                    .customRule((currentValue): any => {
                                      SetValue(
                                        FORM_FIELDS.FIRST_NAME,
                                        currentValue.replace(new RegExp(props.firstNameRegEx, 'g'), ''),
                                      );
                                      return { valid: true };
                                    })
                                : undefined
                            }
                          />
                        </GridCell>,

                        <GridCell tablet={12} desktop={6} key={FORM_FIELDS.LOCAL_FIRST_NAME}>
                          <GeTextInput
                            className="text-required"
                            labelText={props.firstLocalNameLabel}
                            hintText={props.firstNameHelperTextLabel}
                            requiredText={props?.localFirstNameRequired}
                            name={FORM_FIELDS.LOCAL_FIRST_NAME}
                            defaultValue={defaultValues[FORM_FIELDS.LOCAL_FIRST_NAME]}
                            placeHolder={props.firstNamePlaceholder}
                            maxLength={Number(props.lastNameMaxCharacters)}
                            required={!props.firstNameOptional}
                            validate={
                              !props.firstNameOptional
                                ? validationSchema()
                                    .required(props.localFirstNameValidation || 'Local first Name is required')
                                    .customRule((currentValue): any => {
                                      SetValue(
                                        FORM_FIELDS.LOCAL_FIRST_NAME,
                                        currentValue.replace(new RegExp(props.firstNameLocalRegEx, 'g'), ''),
                                      );
                                      return { valid: true };
                                    })
                                : undefined
                            }
                          />
                        </GridCell>,

                        <GridCell tablet={12} desktop={6} key={FORM_FIELDS.LAST_NAME}>
                          <GeTextInput
                            className="text-required"
                            labelText={props.lastNameLabel}
                            hintText={props.lastNameHelperTextLabel}
                            requiredText={props.lastNameOptionalRequiredLabel}
                            name={FORM_FIELDS.LAST_NAME}
                            defaultValue={defaultValues[FORM_FIELDS.LAST_NAME]}
                            placeHolder={props.lastNamePlaceholder}
                            maxLength={Number(props.lastNameMaxCharacters)}
                            required={!props.lastNameOptional}
                            validate={
                              !props.lastNameOptional
                                ? validationSchema()
                                    .required(props.lastNameRequiredValidationMessage || 'Last Name is required')
                                    .customRule((currentValue): any => {
                                      SetValue(
                                        FORM_FIELDS.LAST_NAME,
                                        currentValue.replace(new RegExp(props.lastNameRegEx, 'g'), ''),
                                      );
                                      return { valid: true };
                                    })
                                : undefined
                            }
                          />
                        </GridCell>,

                        <GridCell tablet={12} desktop={6} key={FORM_FIELDS.LOCAL_LAST_NAME}>
                          <GeTextInput
                            className="text-required"
                            labelText={props?.lastLocalNameLabel}
                            hintText={props.lastNameHelperTextLabel}
                            requiredText={props?.localLastNameRequired}
                            name={FORM_FIELDS.LOCAL_LAST_NAME}
                            defaultValue={defaultValues[FORM_FIELDS.LOCAL_LAST_NAME]}
                            placeHolder={props.lastNamePlaceholder}
                            maxLength={Number(props.lastNameMaxCharacters)}
                            required={!props.lastNameOptional}
                            validate={
                              !props.lastNameOptional
                                ? validationSchema()
                                    .required(props.localLastNameValidation || 'Local last Name is required')
                                    .customRule((currentValue): any => {
                                      SetValue(
                                        FORM_FIELDS.LOCAL_LAST_NAME,
                                        currentValue.replace(new RegExp(props.lastNameLocalRegEx, 'g'), ''),
                                      );
                                      return { valid: true };
                                    })
                                : undefined
                            }
                          />
                        </GridCell>,

                        <GridCell tablet={6} desktop={12} key={FORM_FIELDS.PHONE}>
                          <GridRow>
                            <GridCell tablet={6} desktop={6}>
                              {sourceApplications === '' && props.countryCodeDropDown?.length > 0 ? (
                                <FormElement
                                  name={FORM_FIELDS.PHONE}
                                  defaultValue={''}
                                  validate={
                                    !props.phoneNumberOptional
                                      ? validationSchema().required(
                                          isSSSource
                                            ? props.businessPhoneNumberValidationMessage
                                            : props.phoneRequiredValidationMessage,
                                        )
                                      : undefined
                                  }
                                >
                                  {({ handleChange, handleBlur, handleFocus, touched, valid }) => {
                                    const isError = touched[FORM_FIELDS.PHONE] && !valid[FORM_FIELDS.PHONE];
                                    return (
                                      <>
                                        <PhoneNumberWithCountryCodeV1
                                          defaultCountrySelected={defaultCountryCode}
                                          requiredText={props.phoneNumberOptionalRequiredLabel}
                                          phoneNumberHelpText=""
                                          isPhoneNumberRequired={!props.phoneNumberOptional}
                                          dropdownLabel={getInputLabel(props.phoneNumberLabel, true) as string}
                                          countryCodeDropDownList={props.countryCodeDropDown as any}
                                          defaultPhoneNumber={defaultValues[FORM_FIELDS.PHONE] || ''}
                                          maxLengthPhone={props.phoneNumberMaxCharacters}
                                          placeHolder={props.phoneNumberPlaceholder}
                                          name={FORM_FIELDS.PHONE}
                                          handleChange={(v: any) => {
                                            const userCountry = props.countryCodeDropDown?.find(
                                              (c) => c.countryISDCode && v.includes(c.countryISDCode),
                                            );
                                            const replacedVal = v
                                              ?.replace(`${userCountry?.countryISDCode}`, '')
                                              ?.replace(spaceRegex, '');
                                            const value = replacedVal?.replace(numericRegex, '');
                                            handleChange(value ? v : '');
                                            handleBlur();
                                          }}
                                          handlePhoneNumberError={(err: any) => {
                                            setIsPhoneNumberError(err);
                                            if (props.handlePhoneNumberError) {
                                              props.handlePhoneNumberError(err);
                                            }
                                          }}
                                          handleBlur={handleBlur}
                                          handleFocus={handleFocus}
                                          className={`country-phone-number country-phone-number${
                                            isError || isPhoneNumberError ? '--error' : ''
                                          }`}
                                        />
                                        <FormMessage
                                          error={isError}
                                          message={isError && props.phoneRequiredValidationMessage}
                                        />
                                      </>
                                    );
                                  }}
                                </FormElement>
                              ) : (
                                <GeTextInput
                                  className="text-required"
                                  labelText={isSSSource ? props.businessPhoneNumberLabel : props.phoneNumberLabel}
                                  requiredText={props.phoneNumberOptionalRequiredLabel}
                                  name={FORM_FIELDS.PHONE}
                                  inputMode="tel"
                                  defaultValue={defaultValues[FORM_FIELDS.PHONE]}
                                  placeHolder={props.phoneNumberPlaceholder}
                                  maxLength={Number(props.phoneNumberMaxCharacters)}
                                  required={!props.phoneNumberOptional}
                                  validate={
                                    !props.phoneNumberOptional
                                      ? validationSchema()
                                          .required(
                                            isSSSource
                                              ? props.businessPhoneNumberValidationMessage
                                              : props.phoneRequiredValidationMessage,
                                          )
                                          .customRule((currentValue): any => {
                                            SetValue(
                                              FORM_FIELDS.PHONE,
                                              currentValue.replace(new RegExp(numericRegex, 'g'), ''),
                                            );
                                            return { valid: true };
                                          })
                                      : undefined
                                  }
                                />
                              )}
                              <div className="hint-text">
                                <EDSIcon icon={'ico-info-32'} className="help-icon" />
                                <span className="text">{props.phoneNumberHelperTextLabel}</span>
                              </div>
                            </GridCell>
                          </GridRow>
                        </GridCell>,
                      ],
                      websiteCountryCode,
                      INTRO_FIELDS_MAP,
                    )}
                  </GridRow>
                </GridCell>
              </>
            );
          }}
        </FormConnector>
      </GridRow>
    </FormSection>
  );
};
