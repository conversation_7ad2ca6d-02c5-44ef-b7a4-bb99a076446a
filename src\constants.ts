import { keyMirror } from 'cx-dle-common-lib';
import { ProfileCardIconsType } from './components/ProfileCard/CardInfo/models';

export const currentDomain = `${window.location.protocol}//${window.location.hostname}`;
export const isWindow = typeof window !== 'undefined';
export const SETUP_USER_COOKIE_NAME = 'setupaccountdetails';
export const SIGN_IN_FORM_NAME = 'Sign in Form';
export const SIGN_IN_START_TAG = 'SIGN_IN_START';
export const CSM_ADMIN_COOKIE_NAME = 'hascsmaccess';
export const CSM_ACCESS_COOKIE_NAME = 'csmaccesslevel';
export const CSM_READ_ONLY_ACCESS = 'CSMReadOnly';
export const CSM_ADMIN_LEVEL_READWRITE = 'CSMEditor';
export const CSM_USER_DATA_STORAGE_KEY = 'impersonationDetails';
export const CSM_USER_SEARCH_URL = '/csm/search-user';
export const ID_TOKEN_COOKIE = 'id_token';
export const COMMUNICATIONS_TAB = 'COMMUNICATIONS';
export const COUNTRY = {
  USA: 'US',
  AFRICA: 'ZA',
  FRANCE: 'FR',
  TUNISIA: 'TU',
};
export const latamCountryList = ['AR', 'MX', 'CL', 'PR', 'PE', 'CO'];
export const ACCOUNT_MICROAPP = {
  SFDC_LOGIN_URL: '/api/v1/login',
  SFDC_LOGOUT_URL: '/api/v1/logout',
};
export const FORM_FIELDS: any = keyMirror({
  EMAIL: null,
  PHONE: null,
  FIRST_NAME: '',
  LAST_NAME: null,
  LOCAL_FIRST_NAME: null,
  LOCAL_LAST_NAME: null,
  CREATE_PASSWORD: null,
  CONFIRM_PASSWORD: null,
  CUSTOMER_ACCOUNT_NUMBER: null,
  CNPJ_CPF: null,
  ULTRASOUND_SERIAL: null,

  FACILITY_NAME: null,
  ADDRESS_FINDER: null,
  ADDRESS_1: null,
  ADDRESS_2: null,
  CITY: null,
  CITYSTATE: null,
  STATE: null,
  COUNTRY: null,
  ZIP: null,

  THIRD_PARTY: null,
  THIRD_PARTY_NAME: null,

  ROLE: null,
  DEPARTMENT: null,
  FACILITY_TYPE: null,

  TERMS_AND_CONDITIONS: null,
  EMAIL_DELIVERY_CONSENT: null,
  CASL_CONSENT: null,

  CaptchaToken: null,
});
export const GEIDPVIVIDCLUB = 'geidpultrasoundvividclubs';
export const GEIDPVOLUSONCLUB = 'geidpultrasoundvolusonclubs';
export const GEIDPABUSCLUB = 'geidpultrasoundabusclubs';
export const GEIDPVERSANACLUB = 'geidpultrasoundversanaclubs';
export const GEIDPLOGIQCLUB = 'geidpultrasoundlogiqclubs';
export const GEIDPSERVICESHOP = 'gssserviceshop';
export const GLOBAL_SIGNUP_PAGE = 'global/signup';
export const AVAILABLE_COUNTRIES = ['US', 'JP'];
export const FACILITY_TYPE = {
  CUSTOMER: 'Customer',
  DEALER: 'Dealer',
  OTHERS: 'Others',
};

export const VALUE_STRINGS = {
  CUSTOMER: 'Customer',
  DEALER: 'Dealer',
  OTHERS: 'Others',
};
export const MY_EQUIPMENT = 'MyEquipment';
export const CUSTOMER_PORTAL = 'CustomerPortal';
export const LX_APPLICATION = 'GEIDPAdobeCaptivatePrime';
export const ACCOUNT_PAGE_URL = '/account/settings';
export const MY_ORDERS = 'CustomerPortal';
export const TRAINING = 'GEIDPAdobeCaptivatePrime';
export const SECURITY = 'GEHCSecurity';
export const PROGRESSIVE_REGISTRATION_STATUS = {
  PENDING: 'Pending',
  CLOSED: 'Closed',
  IN_PROGRESS: 'In Progress',
  REJECTED: 'Rejected',
  FAILURE: 'Failure',
};

export const FORM_NAME = 'Personal Info';

export const EMAIL_FORM_NAME = 'Email Form';
export const NAME_FORM_NAME = 'Name Form';
export const PHONE_FORM_NAME = 'Phone Name';
export const LANGUAGE_FORM_NAME = 'Language Name';

export const SITE_NAME = {
  TUNISIA: 'gehc-frza',
  FRANCE: 'gehc-frUS',
  AFRICA: 'gehc-enza',
  AMERICA: 'gehc-us',
};

export const GET_CLIENT_COUNTRY_ENDPOINTS = {
  BASE_ENDPOINT: 'gehc_api',
  METHOD: 'GeoIPLocator/GetClientCountry',
};

export const AVURI_DEVICE_MANAGEMENT = 'GEIDPAvuriDeviceManagement';
export const LAUNCH_DARKLY_FLAGS = {
  ENABLE_PARTS_POOL_USAGE_TILE: 'enablePartsPoolUsageTile',
};

export const VIEW_POOL_CONTRACTS_ID = 'VIEW_POOL_CONTRACTS_USAGE';

export const SERVICE_SHOP_ID = 'SERVICE_SHOP';

export const OG_URL = 'og:url';

export const ID_TOKEN = 'id_token';

export const DictionaryKeys = {
  Navigation: {
    Search: {
      DesktopProminentSearch: 'Navigation.Search.DesktopProminentSearch',
      Explore: 'Navigation.Search.Explore',
      GoToSite: 'Navigation.Search.GoToSite',
      QuickLinks: 'Navigation.Search.QuickLinks',
      RecentSearches: 'Navigation.Search.RecentSearches',
      ServiceShop: 'Navigation.Search.ServiceShop',
      ServiceShopURL: 'Navigation.Search.ServiceShopURL',
      Shop: 'Navigation.Search.Shop',
      SuggestedKeywords: 'Navigation.Search.SuggestedKeywords',
    },
  },
};

export const ProfileCardIcons: ProfileCardIconsType = {
  profile: 'ico-placeholderavatar-32',
  help: 'ico-help-32',
  notification: 'ico-notification-16',
};

export const CONTROL_KEYS = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Delete', 'Tab'];
export const DIRECT_USER = 'direct_user';
export const CP_USER = 'cp_user';
export const CP_ADMIN_USER = 'cp_admin_user';
export const CHECK_CYBER_SECURITY_UPDATES = 'CHECK_CYBER_SECURITY_UPDATES';
export const VIEW_MANUALS = 'VIEW_MANUALS';
export const PHONE = 'PHONE';

export const VALID_COUNTRYCODE = ['CA', 'US'];
export const VALID_ISDCODE = ['+1'];

export const breadCrumb = {
  BREAD_CRUMB_NAME: 'header breadcrumb'
}

// Blocked URLs for USCAN regions
export const BLOCKED_ANALYTICS_URLS = [
  '/account/serviceperformance',
  '/serviceperformance',
  '/service-performance',
  '/analyze-service-performance',
  // Add other related URL patterns
];
