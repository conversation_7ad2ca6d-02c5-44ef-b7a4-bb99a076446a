{"name": "@gehc-ux/eds-button", "description": "Button Module for Elements Design System for GE Healthcare", "license": "SEE LICENSE IN @GEHC-UX/eds-button/licensing", "version": "1.0.0", "keywords": ["button", "primary", "secondardy", "tertiary", "icon"], "componentType": "sass", "scripts": {"build-dist": "rsync --exclude=.git/ --exclude=devops-config.yml --exclude=dist/ --exclude=*spec* --exclude=*_api.json --recursive * dist/", "pretest": "npm run test:generate-colormap && npm run test:generate-theme-vars", "pretest-watch": "npm run test:generate-colormap && npm run test:generate-theme-vars", "test": "ng test --code-coverage", "test-watch": "ng test --code-coverage --watch --browsers Chrome", "test-report": "http-server -c-1 -o -p 9875 ./coverage", "test:generate-colormap": "node node_modules/@gehc-ux/eds-tools/tests/generate-color-map.js", "test:generate-colormap:force": "node node_modules/@gehc-ux/eds-tools/tests/generate-color-map.js --force-regen", "test:generate-theme-vars": "cd node_modules/@gehc-ux/eds-tools && node tests/kickoff-webpack.js && node dist/generate-theme-variables-map"}, "repository": {"type": "git", "url": "https://github.build.ge.com/gehc-ux/eds-button"}, "dependencies": {"@gehc-ux/eds-icon": "^1.0.0", "@gehc-ux/eds-core": "^1.3.0"}, "devDependencies": {"@gehc-ux/eds-tools": "^1.0.0", "@gehc-ux/eds-theme-switch": "^1.4.0"}}