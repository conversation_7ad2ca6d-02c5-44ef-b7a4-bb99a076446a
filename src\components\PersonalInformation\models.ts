import { <PERSON><PERSON><PERSON>r, ApolloQueryResult, OperationVariables } from '@apollo/client';
import { UserInfo } from '../../hooks';
import { PersonalInfoType } from '../../models/ProfileTab';
import { ExtensionProps } from './components/PhoneSection/models';

export interface PersonalInformationProps extends PersonalInfoType {
  extension: ExtensionProps;
  userInfo: UserInfo;
  error: ApolloError | undefined;
  loading: boolean;
  refetch: (variables?: Partial<OperationVariables> | undefined) => Promise<ApolloQueryResult<UserInfo>>;
}
