import { AnalyticsDataAttribute, FormValues, SchemaItemFn } from 'cx-dle-common-lib';
import { GeSelectOption } from 'cx-dle-component-library';
import { GeSelectOnChangeFunc } from '../GeSelect/model';
import React from 'react';
import {
  FieldErrors,
  FieldValues,
  UseFormRegister,
  UseFormReset,
  UseFormSetError,
  UseFormSetValue,
  UseFormUnregister,
} from 'react-hook-form';

export type InformationFormFieldType = 'text' | 'select' | 'email';

export type FieldValidator = SchemaItemFn;

export interface InformationFormField {
  title: string;
  type?: InformationFormFieldType;
  name: string;
  value: string;
  options?: GeSelectOption[];
  placeholder?: string;
  hintText?: string;
  requiredLabelText?: string;
  requiredMessageText?: string;
  validationMessageText?: string;
  disabled?: boolean;
  onChangeSelect?: GeSelectOnChangeFunc;
  validateFunc?: FieldValidator;
  validationExtension?: ValidationProps;
}
export interface ValidationProps {
  maxCharacters: number;
  regex: string;
  supportedCountries: string[];
}

export interface InformationEditFormProps {
  analyticsAttributes: AnalyticsDataAttribute[];
  description: string;
  saveButtonText: string;
  cancelButtonText: string;
  fields: InformationFormField[];
  isSubmitting?: boolean;
  handleSubmit: (values: FormValues) => Promise<void>;
  handleCancel?: () => void;
  countryCodeDropDown: Array<{
    flagSource: string;
    minLength: string;
    countryCode: string;
    countryISDCode: string;
    countryName: string;
    maxLength: string;
    validationMessage: string;
    countryPhoneNumberPlaceholder?: string;
  }>;

  siteName?: string;
  countryCode?: string;
  hasExtension?: boolean;
}

export interface UserCountry {
  data: {
    countryCode: string;
    countryName: string;
    websiteCountryCode: string;
  };
}

export interface FormKeyValuePair<T> {
  [fieldKey: string]: T;
}

export interface FormStatus {
  dirty: boolean;
  valid: boolean;
  touched: boolean;
  submitting: boolean;
  lastFieldChanged: string;
}

export interface FormState {
  fields: FormKeyValuePair<any>;
  changed: FormKeyValuePair<boolean>;
  values: FormKeyValuePair<any>;
  valid: FormKeyValuePair<boolean>;
  errors: FieldErrors<FieldValues>;
  touched: FormKeyValuePair<boolean>;
  status: FormStatus;
}

export interface FormContextProps {
  SetValue: UseFormSetValue<FieldValues>;
  SetError: UseFormSetError<FieldValues>;
  RegisterField: UseFormRegister<FieldValues>;
  UnregisterField: UseFormUnregister<FieldValues>;
  ResetForm: UseFormReset<FieldValues>;
  SetTouched: (field: string) => void;
  form: FormState;
}

export interface FormConnectorProps {
  children: (formContext: FormContextProps) => React.ReactNode;
}
