import React from 'react';
import {
  EMAIL_FORM_NAME,
  FORM_NAME,
  LANGUAGE_FORM_NAME,
  NAME_FORM_NAME,
  PHONE_FORM_NAME,
  VALID_ISDCODE,
} from '../../constants';
import { prepareDataAnalyticsAttributes } from 'cx-dle-common-lib';
import { LoadableSection } from 'cx-dle-component-library';
import PreferencesCard from '../Common/PreferencesCard';
import { EmailSection, LanguageSection, NameSection, PhoneSection } from './components';
import { PersonalInformationProps } from './models';

export const PersonalInformationComponent: React.ComponentType<PersonalInformationProps> = (
  props: PersonalInformationProps,
) => {
  // Remove unwanted class on render
  document.getElementsByClassName('ge-tab-container__tab-items')[0]?.classList?.remove('accesstab_banner');
  // Regex for phone formatting
  const numericRegex = /[^\d\s+]/g;

  const ISDCode = props.userInfo?.userAccountDetails?.contactPhone.split(' ')[0] || '';

  const doesISDCodeExist = VALID_ISDCODE.includes(ISDCode);

  return (
    <LoadableSection error={false} loading={props.loading}>
      {() => (
        <PreferencesCard className="personal-information" title={props.title}>
          <div {...prepareDataAnalyticsAttributes([{ name: 'formName', value: FORM_NAME }])}>
            <NameSection
              formName={FORM_NAME}
              subFormName={NAME_FORM_NAME}
              {...props}
              firstName={props.userInfo?.userAccountDetails?.firstName}
              localFirstName="" // {props.userInfo.localFirstName} // TODO for Japan
              lastName={props.userInfo?.userAccountDetails?.lastName}
              localLastName="" // {props.userInfo.localLastName} // TODO for Japan
              updateConfirmationMessage={props.updateConfirmationMessage}
            />
            <EmailSection
              formName={FORM_NAME}
              subFormName={EMAIL_FORM_NAME}
              defaultHideToggle={false}
              sectionStatusInfo={''}
              {...{ ...props, editButtonText: '' }}
              email={props.userInfo?.userAccountDetails?.email}
              updateConfirmationMessage={props.updateConfirmationMessage}
            />
            <PhoneSection
              formName={FORM_NAME}
              subFormName={PHONE_FORM_NAME}
              phone={props.userInfo?.userAccountDetails?.contactPhone?.replace(numericRegex, '') as string}
              phoneExtension={props.userInfo?.userAccountDetails?.extension}
              {...props}
              updateConfirmationMessage={props.updateConfirmationMessage}
              siteName=""
              countryCode={props?.userInfo.userAccountDetails.country}
              ISDCode={doesISDCodeExist}
            />
            {props?.newLanguages?.length > 1 && (
              <LanguageSection
                formName={FORM_NAME}
                subFormName={LANGUAGE_FORM_NAME}
                {...props}
                {...props.userInfo?.userAccountDetails}
                updateConfirmationMessage={props.updateConfirmationMessage}
              />
            )}
          </div>
        </PreferencesCard>
      )}
    </LoadableSection>
  );
};
