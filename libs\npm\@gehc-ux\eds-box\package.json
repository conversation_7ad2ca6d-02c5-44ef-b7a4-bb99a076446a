{"name": "@gehc-ux/eds-box", "description": "Box Module for Elements Design System for GE Healthcare", "license": "SEE LICENSE IN @GEHC-UX/eds-box/licensing", "version": "1.0.0", "keywords": ["button"], "componentType": "sass", "scripts": {"build-dist": "rsync --exclude=.git/ --exclude=devops-config.yml --exclude=dist/ --exclude=*spec* --exclude=*_api.json --recursive * dist/", "test": "ng test --code-coverage", "test-watch": "webpack && ng test --code-coverage --watch --browsers Chrome"}, "repository": {"type": "git", "url": "https://github.build.ge.com/gehc-ux/eds-box"}, "dependencies": {"@gehc-ux/eds-core": "^1.3.0"}, "devDependencies": {"@gehc-ux/eds-tools": "^1.0.0"}}