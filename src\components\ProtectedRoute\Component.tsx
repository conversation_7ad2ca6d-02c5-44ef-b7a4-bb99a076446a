import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { isBlockedAnalyticsUrl } from '../../utils/commonUtils';
import getUserDetailsFromCookies, { isCSMMode } from '../../utils/helpers';
import config from '../../config';
import { Redirect } from '../Redirect';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * ProtectedRoute component that checks for blocked analytics URLs
 * and redirects users to the account page if they try to access blocked content.
 * This serves as a fallback protection in case the initial redirect in index.tsx fails.
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const location = useLocation();
  
  useEffect(() => {
    // Additional check when route changes
    const currentPath = location.pathname;
    const user = getUserDetailsFromCookies();
    const userCountry = isCSMMode() 
      ? user?.userAccountDetails?.country 
      : user?.address?.country;
    
    if (isBlockedAnalyticsUrl(currentPath, userCountry)) {
      console.log('ProtectedRoute: Blocked URL detected, redirecting...', {
        path: currentPath,
        country: userCountry
      });
    }
  }, [location.pathname]);
  
  // Check if current route should be blocked
  const user = getUserDetailsFromCookies();
  const userCountry = isCSMMode() 
    ? user?.userAccountDetails?.country 
    : user?.address?.country;
  
  if (isBlockedAnalyticsUrl(location.pathname, userCountry)) {
    // Redirect to main account page with proper leading slash
    const redirectPath = config?.accountPageRoute?.startsWith('/') 
      ? config.accountPageRoute 
      : `/${config?.accountPageRoute || 'account'}`;
    
    console.log('ProtectedRoute: Redirecting blocked URL to:', redirectPath);
    return <Redirect to={redirectPath} />;
  }
  
  return <>{children}</>;
};
