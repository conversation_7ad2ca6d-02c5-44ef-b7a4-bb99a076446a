import { Cookies } from 'react-cookie';
import { jwtDecode } from 'jwt-decode';

import { isCSMMode, isEUSite, isLASite } from '.';
import config from '../config';
import { AVAILABLE_COUNTRIES, ID_TOKEN_COOKIE, ID_TOKEN, BLOCKED_ANALYTICS_URLS } from '../constants';
import { OMNITURE_EVENTS } from 'cx-dle-common-lib';
import { CommunicationsPreferencesLinkType } from '../models/Account';

export const renderFormFieldsByLocale = (formFields: any, countryCode: string, fieldOrderMap: any) => {
  if (countryCode === 'GLOBAL') {
    return formFields;
  }

  if (!AVAILABLE_COUNTRIES.includes(countryCode)) {
    countryCode = 'US';
  }

  formFields.forEach((item: any, i: number, elems: any) => {
    if (!fieldOrderMap[item['key']]) {
      return;
    }

    const requiredIndex = fieldOrderMap[item['key']][countryCode];
    if (requiredIndex === -1) {
      elems.splice(elems.indexOf(item), 1);
    }

    if (requiredIndex !== -1 && i !== requiredIndex) {
      let temp: any;
      // eslint-disable-next-line prefer-const
      temp = elems[requiredIndex];
      elems[requiredIndex] = elems[i];
      elems[i] = temp;
    }
  });

  return formFields;
};

export const getWebSocketDownloadDocumentSettings = (): any => {
  const webSocketSettings = {
    webSocketUri: isLASite()
      ? config.endpoints.documentProviderLatamWebSocketURI
      : isEUSite()
      ? config.endpoints.documentProviderAfricaWebSocketURI
      : config.endpoints.documentProviderWebSocketURI,
    timeout: config.endpoints.documentProviderTimeOut,
  };
  return webSocketSettings;
};

export const getDecodedCookieValue = (cookieName: string) => {
  if (typeof window !== 'undefined') {
    const userCookie = new Cookies(window.document.cookie).get(cookieName);
    if (userCookie) {
      const base64Url = userCookie.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join(''),
      );
      return JSON.parse(jsonPayload);
    }
    return null;
  }
  return null;
};

export const getUserCountry = () => {
  const idTokenDecodeVal = getDecodedCookieValue(ID_TOKEN_COOKIE);
  return idTokenDecodeVal?.address?.country;
};

export const shouldShowComponent = (supportedCountries: string[] = [], country?: string): boolean => {
  const userCountry = getUserCountry();
  // If supportedCountries is empty, hide the component globally
  if (supportedCountries?.length === 0) return false;
  // If user's country is in the supportedCountries list, show the component
  if (isCSMMode()) {
    return supportedCountries?.includes(country || '');
  }
  return supportedCountries?.includes(userCountry);
};

export const getAnalyticsProps = (item: { title?: string; text?: string }) => {
  return [
    { name: 'trackingEvent', value: OMNITURE_EVENTS.LINK_CLICK },
    { name: 'linkName', value: item?.text },
    { name: 'linkType', value: 'profile settings' },
    { name: 'linkTitle', value: item?.title || 'profile information' },
  ];
};

export const checkApplicationAccess = (
  assignedApplications: { applicationName: string }[],
  applicationsToCheck: string[],
) => {
  return applicationsToCheck?.some((app) =>
    assignedApplications?.some(({ applicationName }) => applicationName === app),
  );
};

export const getApplicationNamesByCountry = (
  tabs: { applicationAccess?: { countryCode: string; applicationName: string }[] }[],
  country: string,
) => {
  return tabs
    ?.filter((tab) => tab?.applicationAccess) //Get tabs that have applicationAccess
    .flatMap((tab) => tab?.applicationAccess) //Flatten the array
    .filter((app) => app?.countryCode === country) // Filter by country code
    .map((app) => app?.applicationName) //Extract only application names
    .flat();
};

export const getApplicationNamesForLinksByCountry = (links: CommunicationsPreferencesLinkType, country: string) => {
  return links?.applicationAccess
    ?.filter((app) => app.countryCode === country) // Filter by country
    .flatMap((app) => app.applicationName); // Extract and flatten applicationName array
};

export const getRegexFromString = (regexString: string) => {
  const flags = '';
  return new RegExp(regexString, flags);
};
export const decodeIdToken = () => {
  const cookies = new Cookies(window.document.cookie);
  const id_token = cookies.get(ID_TOKEN);
  const decodedToken = jwtDecode(id_token);
  return decodedToken;
};

export const isBlockedAnalyticsUrl = (url: string, userCountry: string): boolean => {
  // Handle null/undefined inputs
  if (!url || typeof url !== 'string') {
    console.log('isBlockedAnalyticsUrl: Invalid URL input:', url);
    return false;
  }

  // Check if user is in USCAN region
  const isUSCANRegion = userCountry && ['US', 'CA', 'CA-FR'].includes(userCountry);

  console.log('isBlockedAnalyticsUrl: Country check:', {
    userCountry,
    isUSCANRegion,
    validCountries: ['US', 'CA', 'CA-FR']
  });

  if (!isUSCANRegion) {
    console.log('isBlockedAnalyticsUrl: User not in USCAN region, allowing URL');
    return false;
  }

  // Check if current URL matches any blocked analytics URLs
  const normalizedUrl = url.toLowerCase();
  const isBlocked = BLOCKED_ANALYTICS_URLS.some(blockedUrl =>
    normalizedUrl.includes(blockedUrl.toLowerCase())
  );

  console.log('isBlockedAnalyticsUrl: URL pattern check:', {
    currentUrl: url,
    normalizedUrl,
    blockedPatterns: BLOCKED_ANALYTICS_URLS,
    isBlocked
  });

  // Add debug logging
  if (isBlocked) {
    console.log('🚫 Blocked analytics URL detected:', {
      currentUrl: url,
      userCountry,
      matchedPattern: BLOCKED_ANALYTICS_URLS.find(blockedUrl =>
        normalizedUrl.includes(blockedUrl.toLowerCase())
      )
    });
  } else {
    console.log('✅ URL is allowed for USCAN user');
  }

  return isBlocked;
};
