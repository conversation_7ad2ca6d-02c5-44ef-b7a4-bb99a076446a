export interface PhoneSectionJssProps {
  closeButtonText: string;
  editButtonText: string;
  saveButtonText: string;
  cancelButtonText: string;
  requiredLabelText: string;
  updateConfirmationMessage: string;
  phoneLabelText: string;
  phoneSectionDescription: string;
  newPhoneLabelText: string;
  newPhonePlaceholderText: string;
  newPhoneHelperText: string;
  emptyNewPhoneErrorMessage: string;
}

export interface PhoneSectionProps extends PhoneSectionJssProps {
  phone: string;
  siteName: string;
  phoneExtension?: string | undefined;
  refetch: () => void;
  extension: ExtensionProps;
  formName: string;
  subFormName: string;
  countryCode: string;
  ISDCode?: boolean;
}

export interface ExtensionProps {
  phoneExtensionLabelText: string;
  phoneExtensionOptionLabel: string;
  validations: ValidationProps[];
}

export interface ValidationProps {
  maxCharacters: number;
  regex: RegExp;
  supportedCountries: string[];
}
