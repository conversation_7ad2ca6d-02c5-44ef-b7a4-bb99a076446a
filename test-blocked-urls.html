<!DOCTYPE html>
<html>
<head>
    <title>Test Blocked Analytics URLs</title>
</head>
<body>
    <h1>Test Blocked Analytics URLs</h1>
    <p>Open the browser console to see test results.</p>
    
    <script>
        // Mock the blocked URLs constant
        const BLOCKED_ANALYTICS_URLS = [
            '/account/serviceperformance',
            '/serviceperformance',
            '/service-performance',
            '/analyze-service-performance',
        ];

        // Mock the isBlockedAnalyticsUrl function
        function isBlockedAnalyticsUrl(url, userCountry) {
            // Handle null/undefined inputs
            if (!url || typeof url !== 'string') {
                return false;
            }
            
            // Check if user is in USCAN region
            const isUSCANRegion = userCountry && ['US', 'CA', 'CA-FR'].includes(userCountry);
            
            if (!isUSCANRegion) {
                return false;
            }
            
            // Check if current URL matches any blocked analytics URLs
            const normalizedUrl = url.toLowerCase();
            const isBlocked = BLOCKED_ANALYTICS_URLS.some(blockedUrl => 
                normalizedUrl.includes(blockedUrl.toLowerCase())
            );
            
            // Add debug logging
            if (isBlocked) {
                console.log('Blocked analytics URL detected:', {
                    currentUrl: url,
                    userCountry,
                    matchedPattern: BLOCKED_ANALYTICS_URLS.find(blockedUrl => 
                        normalizedUrl.includes(blockedUrl.toLowerCase())
                    )
                });
            }
            
            return isBlocked;
        }

        // Test cases
        const testCases = [
            // Should be blocked for US/CA users
            { url: '/account/serviceperformance', country: 'US', expected: true },
            { url: '/serviceperformance', country: 'CA', expected: true },
            { url: '/service-performance', country: 'CA-FR', expected: true },
            { url: '/analyze-service-performance', country: 'US', expected: true },
            { url: '/en-us/account/serviceperformance', country: 'US', expected: true },
            { url: '/fr-ca/serviceperformance', country: 'CA', expected: true },
            
            // Should NOT be blocked for other countries
            { url: '/account/serviceperformance', country: 'FR', expected: false },
            { url: '/serviceperformance', country: 'DE', expected: false },
            { url: '/service-performance', country: 'JP', expected: false },
            
            // Should NOT be blocked for other URLs
            { url: '/account/settings', country: 'US', expected: false },
            { url: '/account/notifications', country: 'CA', expected: false },
            { url: '/account', country: 'US', expected: false },
            
            // Edge cases
            { url: '', country: 'US', expected: false },
            { url: '/account/serviceperformance', country: '', expected: false },
            { url: '/account/serviceperformance', country: null, expected: false },
        ];

        // Run tests
        console.log('🧪 Testing blocked analytics URL functionality...');
        
        let passed = 0;
        let failed = 0;
        
        testCases.forEach((testCase, index) => {
            const result = isBlockedAnalyticsUrl(testCase.url, testCase.country);
            const success = result === testCase.expected;
            
            if (success) {
                passed++;
                console.log(`✅ Test ${index + 1}: PASSED`);
            } else {
                failed++;
                console.log(`❌ Test ${index + 1}: FAILED`);
                console.log(`   URL: ${testCase.url}`);
                console.log(`   Country: ${testCase.country}`);
                console.log(`   Expected: ${testCase.expected}`);
                console.log(`   Got: ${result}`);
            }
        });
        
        console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
        
        // Test redirection logic
        console.log('\n🔄 Testing redirection logic...');
        
        const redirectTestCases = [
            { url: '/account/serviceperformance', country: 'US' },
            { url: '/serviceperformance', country: 'CA' },
            { url: '/account/settings', country: 'US' },
        ];
        
        redirectTestCases.forEach((testCase) => {
            console.log(`\n🌐 Simulating navigation to: ${testCase.url} for user in country: ${testCase.country}`);
            
            if (isBlockedAnalyticsUrl(testCase.url, testCase.country)) {
                console.log('🚫 URL is blocked - would redirect to /account');
            } else {
                console.log('✅ URL is allowed - would proceed normally');
            }
        });
    </script>
</body>
</html>
